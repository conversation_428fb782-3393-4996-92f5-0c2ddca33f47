using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class GameService : IGameService
    {
        private readonly ILobbyService _lobbyService;
        private readonly ICardService _cardService;
        private readonly IBallService _ballService;
        private readonly ITurnService _turnService;
        private readonly ITimeframeService _timeframeService;

        public GameService(
            ILobbyService lobbyService,
            ICardService cardService,
            IBallService ballService,
            ITurnService turnService,
            ITimeframeService timeframeService)
        {
            _lobbyService = lobbyService;
            _cardService = cardService;
            _ballService = ballService;
            _turnService = turnService;
            _timeframeService = timeframeService;
        }

        // Events
        public event Func<string, TimeframeOptionsResponse, Task>? TimeframeOptionsUpdated;
        public event Func<string, TimeframeVoteResponse, Task>? TimeframeVoteUpdated;
        public event Func<string, CardDealtResponse, Task>? CardDealt;
        public event Func<string, DealerFoundResponse, Task>? DealerFound;
        public event Func<string, CardsDealtResponse, Task>? CardsDealt;
        public event Func<string, TrumpSelectedResponse, Task>? TrumpSelected;
        public event Func<string, PlayerTurnResponse, Task>? PlayerTurn;
        public event Func<string, CardPlayedResponse, Task>? CardPlayed;
        public event Func<string, HandCompletedResponse, Task>? HandCompleted;
        public event Func<string, BallCompletedResponse, Task>? BallCompleted;
        public event Func<string, GameEndedResponse, Task>? GameEnded;
        public event Func<string, JordhiCalledResponse, Task>? JordhiCalled;
        public event Func<string, JordhiCardsRevealedResponse, Task>? JordhiCardsRevealed;
        public event Func<string, FourBallResultResponse, Task>? FourBallResult;
        public event Func<string, GamePhaseUpdatedResponse, Task>? GamePhaseUpdated;
        public event Func<string, Task>? DealerDeterminationReset;
        public event Func<string, DealerDeterminationStartedResponse, Task>? DealerDeterminationStarted;
        public event Func<string, DealingCardToResponse, Task>? DealingCardTo;
        public event Func<string, DealerDeterminationAbortedResponse, Task>? DealerDeterminationAborted;

        public async Task<bool> VoteTimeframeAsync(string connectionId, int timeframe)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            var success = _timeframeService.RecordTimeframeVote(lobby, connectionId, timeframe);
            if (!success) return false;

            // Update vote results
            _timeframeService.UpdateVoteResults(lobby);

            // Check if all players have voted
            if (_timeframeService.AllPlayersVoted(lobby))
            {
                var selectedTimeframe = _timeframeService.DetermineSelectedTimeframe(lobby);
                if (selectedTimeframe.HasValue)
                {
                    // Store the selected timeframe
                    if (lobby.TimeframeVoting != null)
                    {
                        lobby.TimeframeVoting.SelectedTimeframe = selectedTimeframe.Value;
                        lobby.TimeframeVoting.IsComplete = true;
                    }

                    // Trigger event to notify all players
                    if (TimeframeVoteUpdated != null)
                    {
                        await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                        {
                            Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                            VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                            AllPlayersVoted = true,
                            SelectedTimeframe = selectedTimeframe.Value
                        });
                    }

                    // After a delay, transition to dealer determination phase
                    _ = Task.Delay(3000).ContinueWith(async _ =>
                    {
                        if (GamePhaseUpdated != null)
                        {
                            await GamePhaseUpdated(lobby.LobbyCode, new GamePhaseUpdatedResponse
                            {
                                Phase = "dealer-determination",
                                Players = lobby.Players
                            });
                        }
                    });
                }
            }
            else
            {
                // Trigger event to update vote counts
                if (TimeframeVoteUpdated != null)
                {
                    await TimeframeVoteUpdated(lobby.LobbyCode, new TimeframeVoteResponse
                    {
                        Votes = lobby.TimeframeVoting?.Votes ?? new Dictionary<string, int>(),
                        VoteCounts = lobby.TimeframeVoting?.VoteCounts ?? new Dictionary<int, int>(),
                        AllPlayersVoted = false,
                        SelectedTimeframe = null
                    });
                }
            }

            return true;
        }

        public async Task<bool> RequestTimeframeOptionsAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
            var votingTimeLimit = lobby.TimeSettings?.VotingTimeLimit ?? 15;

            if (TimeframeOptionsUpdated != null)
            {
                await TimeframeOptionsUpdated(lobby.LobbyCode, new TimeframeOptionsResponse
                {
                    TimeOptions = timeOptions,
                    VotingTimeLimit = votingTimeLimit
                });
            }

            return true;
        }

        public async Task<bool> PlayCardAsync(string connectionId, Card card)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that it's the player's turn
            if (lobby.TurnTimerState?.CurrentPlayerId != connectionId)
            {
                return false;
            }

            // Find and remove the card from player's hand
            var playerCard = _cardService.FindCardInPlayerHand(connectionId, card, lobby.PlayerCards);
            if (playerCard == null) return false;

            var success = _cardService.RemoveCardFromPlayerHand(connectionId, card, lobby.PlayerCards);
            if (!success) return false;

            // Add card to current hand
            if (lobby.CurrentHandCards == null)
            {
                lobby.CurrentHandCards = new List<Card>();
            }

            playerCard.PlayedBy = connectionId;
            lobby.CurrentHandCards.Add(playerCard);

            // Trigger card played event
            if (CardPlayed != null)
            {
                await CardPlayed(lobby.LobbyCode, new CardPlayedResponse
                {
                    PlayerId = connectionId,
                    PlayerName = player.Name,
                    PlayerTeam = player.Team,
                    Card = playerCard,
                    HandId = lobby.CurrentHandId,
                    HandCards = lobby.CurrentHandCards
                });
            }

            // Check if hand is complete (4 cards played)
            if (lobby.CurrentHandCards.Count == 4)
            {
                await ProcessHandCompletionAsync(lobby);
            }
            else
            {
                // Set next player's turn
                await SetNextPlayerTurnAsync(lobby);
            }

            return true;
        }

        public async Task<bool> SelectTrumpAsync(string connectionId, string trumpSuit)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            var player = lobby.Players.FirstOrDefault(p => p.Id == connectionId);
            if (player == null) return false;

            // Validate that this player can select trump
            if (lobby.TrumpSelectorId != connectionId)
            {
                return false;
            }

            lobby.TrumpSuit = trumpSuit;

            // Trigger trump selected event
            if (TrumpSelected != null)
            {
                await TrumpSelected(lobby.LobbyCode, new TrumpSelectedResponse
                {
                    TrumpSuit = trumpSuit,
                    TrumpSelectorId = connectionId,
                    TrumpSelectorName = player.Name
                });
            }

            return true;
        }

        public Task<bool> BidAsync(string connectionId, int bidAmount)
        {
            // Implementation for bidding
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> PassBidAsync(string connectionId)
        {
            // Implementation for passing bid
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> ShuffleCardsAsync(string connectionId, string shuffleType)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return Task.FromResult(false);

            // Validate that this player can shuffle (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return Task.FromResult(false);
            }

            // Create and shuffle a new deck
            var deck = _cardService.CreateGameDeck();
            lobby.GameDeck = _cardService.ShuffleDeck(deck);

            return Task.FromResult(true);
        }

        public Task<bool> CutCardsAsync(string connectionId, int cutPosition)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return Task.FromResult(false);

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                return Task.FromResult(false);
            }

            lobby.GameDeck = _cardService.CutDeck(lobby.GameDeck, cutPosition);

            return Task.FromResult(true);
        }

        public async Task<bool> DealCardsAsync(string connectionId, int cardsPerPlayer)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Validate that this player can deal (usually the dealer)
            if (lobby.DealerId != connectionId)
            {
                return false;
            }

            if (lobby.GameDeck == null || lobby.GameDeck.Count == 0)
            {
                // Create a new deck if none exists
                lobby.GameDeck = _cardService.CreateGameDeck();
            }

            // Deal cards to all players
            lobby.PlayerCards = _cardService.DealCardsToPlayers(lobby.Players, lobby.GameDeck, cardsPerPlayer, lobby.PlayerCards);

            // Trigger cards dealt event
            if (CardsDealt != null)
            {
                await CardsDealt(lobby.LobbyCode, new CardsDealtResponse
                {
                    PlayerCards = lobby.PlayerCards,
                    CardsPerPlayer = cardsPerPlayer
                });
            }

            return true;
        }

        public Task<bool> CallJordhiAsync(string connectionId, int value, string jordhiSuit, List<Card> jordhiCards)
        {
            // Implementation for Jordhi call
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> RevealJordhiAsync(string connectionId, int jordhiValue, string jordhiSuit, List<Card> jordhiCards, bool revealCards)
        {
            // Implementation for Jordhi reveal
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> CallDoubleAsync(string connectionId)
        {
            // Implementation for Double call
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> CallKhanakAsync(string connectionId)
        {
            // Implementation for Khanak call
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> CallThuneeAsync(string connectionId, Card firstCard)
        {
            // Implementation for Thunee call
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> FourBallAsync(string connectionId, string ballType, string option, string accusedPlayerId, int handNumber)
        {
            // Implementation for 4-ball call
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> SetDealerAsync(string connectionId, string dealerId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return Task.FromResult(false);

            lobby.DealerId = dealerId;
            lobby.DealerFound = true;

            var dealer = lobby.Players.FirstOrDefault(p => p.Id == dealerId);
            if (dealer != null)
            {
                dealer.IsDealer = true;
            }

            return Task.FromResult(true);
        }

        public async Task<bool> StartDealerDeterminationAsync(string connectionId)
        {
            var lobby = _lobbyService.GetLobbyByConnectionId(connectionId);
            if (lobby == null) return false;

            // Check if dealer determination is already in progress
            if (lobby.DealerDeterminationInProgress)
            {
                return false;
            }

            // Get all players from both teams
            var allPlayers = new List<Player>();
            if (lobby.Teams.ContainsKey(1))
                allPlayers.AddRange(lobby.Teams[1]);
            if (lobby.Teams.ContainsKey(2))
                allPlayers.AddRange(lobby.Teams[2]);

            // Ensure we have exactly 4 players
            if (allPlayers.Count != 4)
            {
                return false;
            }

            // Create a shuffled deck for dealer determination
            lobby.DealerDeck = _cardService.CreateGameDeck();

            // Choose a random starting player index (0-3)
            var random = new Random();
            lobby.CurrentDealerPlayerIndex = random.Next(allPlayers.Count);
            lobby.DealerFound = false;
            lobby.DealerId = null;
            lobby.DealerDeterminationInProgress = true;

            // Initialize cards dealt counter for each player
            lobby.CardsDealtPerPlayer = new Dictionary<string, int>();
            foreach (var player in allPlayers)
            {
                lobby.CardsDealtPerPlayer[player.Id] = 0;
            }

            // First, reset any existing dealer determination state
            if (DealerDeterminationReset != null)
            {
                await DealerDeterminationReset(lobby.LobbyCode);
            }

            // After a short delay, send the dealer determination data
            await Task.Delay(1000);

            // Send the dealer determination data to all clients
            if (DealerDeterminationStarted != null)
            {
                await DealerDeterminationStarted(lobby.LobbyCode, new DealerDeterminationStartedResponse
                {
                    Players = allPlayers,
                    CurrentPlayerIndex = lobby.CurrentDealerPlayerIndex
                });
            }

            // Start the dealer determination process after another short delay
            await Task.Delay(1000);
            await DealNextCardAsync(lobby, allPlayers);

            return true;
        }

        private async Task DealNextCardAsync(Lobby lobby, List<Player> allPlayers)
        {
            // If dealer already found, do nothing
            if (lobby.DealerFound)
            {
                lobby.DealerDeterminationInProgress = false;
                return;
            }

            // Safety check for deck
            if (lobby.DealerDeck == null || lobby.DealerDeck.Count == 0)
            {
                // Abort dealer determination
                if (DealerDeterminationAborted != null)
                {
                    await DealerDeterminationAborted(lobby.LobbyCode, new DealerDeterminationAbortedResponse
                    {
                        Reason = "No cards left in deck"
                    });
                }
                lobby.DealerDeterminationInProgress = false;
                return;
            }

            // Get the current player
            var currentPlayerIndex = lobby.CurrentDealerPlayerIndex;
            if (currentPlayerIndex >= allPlayers.Count)
            {
                lobby.DealerDeterminationInProgress = false;
                return;
            }

            var currentPlayer = allPlayers[currentPlayerIndex];

            // Check if this player already has too many cards (safety check)
            if (lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id) &&
                lobby.CardsDealtPerPlayer[currentPlayer.Id] >= 24)
            {
                // Move to the next player
                lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;

                // Schedule the next card deal
                await Task.Delay(500);
                await DealNextCardAsync(lobby, allPlayers);
                return;
            }

            // First, notify all clients that we're about to deal a card to this player
            if (DealingCardTo != null)
            {
                await DealingCardTo(lobby.LobbyCode, new DealingCardToResponse
                {
                    PlayerId = currentPlayer.Id,
                    PlayerName = currentPlayer.Name,
                    PlayerTeam = currentPlayer.Team,
                    PlayerIndex = currentPlayerIndex
                });
            }

            // After a short delay, deal the actual card
            await Task.Delay(500);

            // Get the next card from the deck
            var card = lobby.DealerDeck.Last();
            lobby.DealerDeck.RemoveAt(lobby.DealerDeck.Count - 1);

            // Check if this is a black Jack (Jack of Clubs or Jack of Spades)
            var isBlackJack = card.Value == "J" && (card.Suit == "clubs" || card.Suit == "spades");

            // Broadcast the card dealt to all players
            if (CardDealt != null)
            {
                await CardDealt(lobby.LobbyCode, new CardDealtResponse
                {
                    PlayerId = currentPlayer.Id,
                    PlayerName = currentPlayer.Name,
                    PlayerTeam = currentPlayer.Team,
                    PlayerIndex = currentPlayerIndex,
                    Card = card,
                    IsDealer = isBlackJack,
                    CardsDealtToPlayer = lobby.CardsDealtPerPlayer.GetValueOrDefault(currentPlayer.Id, 0) + 1,
                    TotalCardsDealt = lobby.CardsDealtPerPlayer.Values.Sum() + 1
                });
            }

            // Increment the card count for this player
            if (!lobby.CardsDealtPerPlayer.ContainsKey(currentPlayer.Id))
                lobby.CardsDealtPerPlayer[currentPlayer.Id] = 0;
            lobby.CardsDealtPerPlayer[currentPlayer.Id]++;

            // If this is a black Jack, we found our dealer
            if (isBlackJack)
            {
                lobby.DealerFound = true;
                lobby.DealerId = currentPlayer.Id;

                // Determine the trump selector (player from opposite team)
                var team = currentPlayer.Team;
                var oppositeTeam = team == 1 ? 2 : 1;
                var oppositeTeamPlayers = allPlayers.Where(p => p.Team == oppositeTeam).ToList();

                // Choose the first player from the opposite team as trump selector
                var trumpSelector = oppositeTeamPlayers.FirstOrDefault();
                if (trumpSelector == null)
                {
                    // Fallback: use the dealer if no opposite team players
                    trumpSelector = currentPlayer;
                }

                // Broadcast dealer found to all players
                if (DealerFound != null)
                {
                    await DealerFound(lobby.LobbyCode, new DealerFoundResponse
                    {
                        DealerId = currentPlayer.Id,
                        DealerName = currentPlayer.Name,
                        DealerTeam = currentPlayer.Team,
                        TrumpSelectorId = trumpSelector.Id,
                        TrumpSelectorName = trumpSelector.Name,
                        TrumpSelectorTeam = trumpSelector.Team,
                        DealerCard = card,
                        Players = allPlayers.Select(p => new Player
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Team = p.Team,
                            IsDealer = p.Id == currentPlayer.Id,
                            IsTrumpSelector = p.Id == trumpSelector.Id
                        }).ToList()
                    });
                }

                lobby.DealerDeterminationInProgress = false;
                return;
            }

            // Move to the next player in clockwise order
            lobby.CurrentDealerPlayerIndex = (currentPlayerIndex + 1) % allPlayers.Count;

            // Schedule the next card deal after a delay
            await Task.Delay(1500);
            await DealNextCardAsync(lobby, allPlayers);
        }

        public Task<bool> PassThuneeAsync(string connectionId)
        {
            // Implementation for passing Thunee
            return Task.FromResult(false); // Placeholder
        }

        public Task<bool> HoldGameAsync(string connectionId)
        {
            // Implementation for holding game (Thunee call)
            return Task.FromResult(false); // Placeholder
        }

        private async Task ProcessHandCompletionAsync(Lobby lobby)
        {
            // Determine hand winner
            // This would use game logic to determine the winner based on trump suit, lead suit, etc.

            // For now, just pick the first player as winner (placeholder)
            var winnerCard = lobby.CurrentHandCards?.FirstOrDefault();
            if (winnerCard?.PlayedBy == null) return;

            var winner = lobby.Players.FirstOrDefault(p => p.Id == winnerCard.PlayedBy);
            if (winner == null) return;

            // Calculate hand points
            var handPoints = lobby.CurrentHandCards?.Sum(c => c.Points) ?? 0;

            // Create hand record
            var hand = new Hand
            {
                Id = lobby.CurrentHandId,
                Cards = lobby.CurrentHandCards ?? new List<Card>(),
                WinnerId = winner.Id,
                WinnerName = winner.Name,
                WinnerTeam = winner.Team,
                Points = handPoints
            };

            lobby.Hands.Add(hand);

            // Update ball points
            var teamKey = $"team{winner.Team}";
            if (!lobby.BallPoints.ContainsKey(teamKey))
            {
                lobby.BallPoints[teamKey] = 0;
            }
            lobby.BallPoints[teamKey] += handPoints;

            // Trigger hand completed event
            if (HandCompleted != null)
            {
                await HandCompleted(lobby.LobbyCode, new HandCompletedResponse
                {
                    HandId = lobby.CurrentHandId,
                    WinnerId = winner.Id,
                    WinnerName = winner.Name,
                    WinnerTeam = winner.Team,
                    HandCards = lobby.CurrentHandCards ?? new List<Card>(),
                    Points = handPoints,
                    NextPlayerId = winner.Id // Winner leads next hand
                });
            }

            // Reset for next hand
            lobby.CurrentHandCards = new List<Card>();
            lobby.CurrentHandId++;

            // Check if ball is complete (6 hands)
            if (lobby.Hands.Count >= 6)
            {
                await ProcessBallCompletionAsync(lobby);
            }
            else
            {
                // Set winner as next player to play
                await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, winner.Id);
            }
        }

        private async Task ProcessBallCompletionAsync(Lobby lobby)
        {
            var ballResult = _ballService.CalculateBallWinner(lobby, lobby.Players);

            // Trigger ball completed event
            if (BallCompleted != null)
            {
                await BallCompleted(lobby.LobbyCode, ballResult);
            }

            // Check if game is complete
            if (_ballService.CheckGameEnd(lobby))
            {
                var winningTeam = lobby.BallScores["team1"] >= 12 ? 1 : 2;

                if (GameEnded != null)
                {
                    await GameEnded(lobby.LobbyCode, new GameEndedResponse
                    {
                        WinningTeam = winningTeam,
                        FinalBallScores = lobby.BallScores,
                        GameHistory = _ballService.GetGameHistory(lobby),
                        Reason = "Ball limit reached"
                    });
                }
            }
            else
            {
                // Reset for next ball
                lobby.Hands.Clear();
                lobby.CurrentHandId = 1;
                lobby.BallPoints = new Dictionary<string, int> { { "team1", 0 }, { "team2", 0 } };
                lobby.CurrentBallId++;
            }
        }

        private async Task SetNextPlayerTurnAsync(Lobby lobby)
        {
            // Determine next player in turn order
            // This would use game logic to determine the next player

            // For now, just cycle through players (placeholder)
            var currentPlayerIndex = lobby.Players.FindIndex(p => p.Id == lobby.TurnTimerState?.CurrentPlayerId);
            var nextPlayerIndex = (currentPlayerIndex + 1) % lobby.Players.Count;
            var nextPlayer = lobby.Players[nextPlayerIndex];

            await _turnService.SetPlayerTurnAsync(lobby.LobbyCode, lobby.LobbyCode, nextPlayer.Id);
        }
    }
}
