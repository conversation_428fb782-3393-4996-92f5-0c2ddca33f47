using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class CardService : ICardService
    {
        public List<Card> CreateGameDeck()
        {
            // <PERSON><PERSON><PERSON> uses 9, 10, J, Q, K, A from each suit
            var suits = new[] { "hearts", "diamonds", "clubs", "spades" };
            var values = new[] { "9", "10", "J", "Q", "K", "A" };
            var points = new Dictionary<string, int>
            {
                { "9", 20 },
                { "10", 10 },
                { "J", 30 },
                { "Q", 2 },
                { "K", 3 },
                { "A", 11 }
            };

            var deck = new List<Card>();
            var id = 1;

            foreach (var suit in suits)
            {
                foreach (var value in values)
                {
                    deck.Add(new Card
                    {
                        Id = $"card_{id++}",
                        Suit = suit,
                        Value = value,
                        Points = points[value],
                        Image = GetCardImagePath(value, suit)
                    });
                }
            }

            return ShuffleDeck(deck);
        }

        public List<Card> ShuffleDeck(List<Card> deck)
        {
            var shuffled = new List<Card>(deck);
            var random = new Random();

            for (int i = shuffled.Count - 1; i > 0; i--)
            {
                int j = random.Next(i + 1);
                (shuffled[i], shuffled[j]) = (shuffled[j], shuffled[i]);
            }

            return shuffled;
        }

        public List<Card> CutDeck(List<Card> deck, int cutPosition)
        {
            if (cutPosition <= 0 || cutPosition >= deck.Count)
            {
                return deck; // Invalid cut position, return original deck
            }

            var cutDeck = new List<Card>();
            cutDeck.AddRange(deck.Skip(cutPosition));
            cutDeck.AddRange(deck.Take(cutPosition));

            return cutDeck;
        }

        public Dictionary<string, List<Card>> DealCardsToPlayers(List<Player> players, List<Card> deck, int cardsPerPlayer, Dictionary<string, List<Card>>? existingPlayerCards = null)
        {
            var playerCards = existingPlayerCards ?? new Dictionary<string, List<Card>>();
            var deckCopy = new List<Card>(deck);

            // Initialize player hands if they don't exist
            foreach (var player in players)
            {
                if (!playerCards.ContainsKey(player.Id))
                {
                    playerCards[player.Id] = new List<Card>();
                }
            }

            // Track already dealt cards to avoid duplicates
            var allDealtCards = GetAllDealtCardKeys(playerCards);

            // Deal cards in rounds (one card to each player at a time)
            for (int round = 0; round < cardsPerPlayer; round++)
            {
                foreach (var player in players)
                {
                    if (deckCopy.Count > 0)
                    {
                        Card? card = null;
                        string cardKey;

                        // Find a unique card that hasn't been dealt yet
                        do
                        {
                            if (deckCopy.Count == 0) break;

                            card = deckCopy[0];
                            deckCopy.RemoveAt(0);
                            cardKey = $"{card.Value}_{card.Suit}";

                            // If this card has already been dealt, try another
                            if (allDealtCards.Contains(cardKey))
                            {
                                Console.WriteLine($"Card {card.Value} of {card.Suit} already dealt, trying another");
                                card = null;
                            }
                        } while (card == null && deckCopy.Count > 0);

                        if (card != null)
                        {
                            playerCards[player.Id].Add(card);
                            allDealtCards.Add($"{card.Value}_{card.Suit}");
                        }
                    }
                }
            }

            return playerCards;
        }

        public List<Card> RemoveDuplicateCards(List<Card> deck)
        {
            var uniqueCards = new List<Card>();
            var cardKeys = new HashSet<string>();

            foreach (var card in deck)
            {
                var key = $"{card.Value}_{card.Suit}";
                if (!cardKeys.Contains(key))
                {
                    cardKeys.Add(key);
                    uniqueCards.Add(card);
                }
                else
                {
                    Console.WriteLine($"Skipping duplicate card: {card.Value} of {card.Suit}");
                }
            }

            return uniqueCards;
        }

        public HashSet<string> GetAllDealtCardKeys(Dictionary<string, List<Card>> playerCards)
        {
            var allDealtCards = new HashSet<string>();

            foreach (var kvp in playerCards)
            {
                if (kvp.Value != null)
                {
                    foreach (var card in kvp.Value)
                    {
                        if (card != null && !string.IsNullOrEmpty(card.Value) && !string.IsNullOrEmpty(card.Suit))
                        {
                            var cardKey = $"{card.Value}_{card.Suit}";
                            allDealtCards.Add(cardKey);
                        }
                        else
                        {
                            Console.WriteLine($"Invalid card found for player {kvp.Key}, skipping");
                        }
                    }
                }
                else
                {
                    Console.WriteLine($"Invalid cards array for player {kvp.Key}, skipping");
                }
            }

            return allDealtCards;
        }

        public Card? FindCardInPlayerHand(string playerId, Card card, Dictionary<string, List<Card>> playerCards)
        {
            if (!playerCards.TryGetValue(playerId, out var playerHand))
            {
                return null;
            }

            return playerHand.FirstOrDefault(c => c.Value == card.Value && c.Suit == card.Suit);
        }

        public bool RemoveCardFromPlayerHand(string playerId, Card card, Dictionary<string, List<Card>> playerCards)
        {
            if (!playerCards.TryGetValue(playerId, out var playerHand))
            {
                return false;
            }

            var cardToRemove = playerHand.FirstOrDefault(c => c.Value == card.Value && c.Suit == card.Suit);
            if (cardToRemove == null)
            {
                return false;
            }

            return playerHand.Remove(cardToRemove);
        }

        public string GetCardImagePath(string value, string suit)
        {
            // Convert suit to first letter uppercase
            var suitFirstLetter = suit.Substring(0, 1).ToUpper();

            // Map card values to their SVG file names
            var cardValue = value == "10" ? "T" : value; // 10 is represented as T in the SVG filenames

            // Return the path to the SVG file
            return $"/CardFace/{cardValue}{suitFirstLetter}.svg";
        }
    }
}
